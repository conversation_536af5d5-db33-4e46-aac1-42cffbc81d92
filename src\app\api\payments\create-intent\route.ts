import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createPaymentIntent } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createIntentSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1)
  })).min(1, 'At least one item is required'),
  customerInfo: z.object({
    email: z.string().email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1)
  }),
  couponCode: z.string().optional()
})

export async function POST(request: NextRequest) {
  // Set CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  }

  try {
    // Get session (optional for guest checkout)
    const session = await getServerSession(authOptions)
    const body = await request.json()
    const { items, customerInfo, couponCode } = createIntentSchema.parse(body)

    // Fetch products and calculate total
    const productIds = items.map(item => item.productId)
    const products = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        status: 'PUBLISHED'
      }
    })

    if (products.length !== items.length) {
      return NextResponse.json({
        success: false,
        message: 'Some products are not available'
      }, {
        status: 400,
        headers: corsHeaders
      })
    }

    // Calculate subtotal amount
    let subtotalAmount = 0
    const orderItems = items.map(item => {
      const product = products.find(p => p.id === item.productId)!
      const price = product.isOnSale && product.salePrice
        ? product.salePrice
        : product.price

      subtotalAmount += Number(price) * item.quantity

      return {
        productId: product.id,
        productName: product.name,
        quantity: item.quantity,
        price: Number(price)
      }
    })

    // Handle coupon validation and discount calculation
    let coupon = null
    let discountAmount = 0
    let finalAmount = subtotalAmount

    if (couponCode) {
      // Validate coupon
      coupon = await prisma.coupon.findUnique({
        where: { code: couponCode.toUpperCase() },
        include: {
          couponUsages: {
            where: session?.user?.id ? { userId: session.user.id } : undefined
          }
        }
      })

      if (!coupon || !coupon.isActive) {
        return NextResponse.json({
          success: false,
          message: 'Invalid or inactive coupon code'
        }, { status: 400 })
      }

      // Check expiry
      if (coupon.expiresAt && new Date() > coupon.expiresAt) {
        return NextResponse.json({
          success: false,
          message: 'This coupon has expired'
        }, { status: 400 })
      }

      // Check usage limits
      if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
        return NextResponse.json({
          success: false,
          message: 'This coupon has reached its usage limit'
        }, { status: 400 })
      }

      // Check minimum amount
      if (coupon.minimumAmount && subtotalAmount < Number(coupon.minimumAmount)) {
        return NextResponse.json({
          success: false,
          message: `Minimum order amount of $${coupon.minimumAmount} required`
        }, { status: 400 })
      }

      // Calculate discount
      if (coupon.type === 'FIXED_AMOUNT') {
        discountAmount = Number(coupon.value)
      } else if (coupon.type === 'PERCENTAGE') {
        discountAmount = (subtotalAmount * Number(coupon.value)) / 100

        if (coupon.maximumDiscount && discountAmount > Number(coupon.maximumDiscount)) {
          discountAmount = Number(coupon.maximumDiscount)
        }
      }

      discountAmount = Math.min(discountAmount, subtotalAmount)
      finalAmount = subtotalAmount - discountAmount
    }

    // Create order record
    const order = await prisma.order.create({
      data: {
        orderNumber: `ORD-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        userId: session?.user?.id || null, // Allow null for guest checkout
        email: customerInfo.email,
        firstName: customerInfo.firstName,
        lastName: customerInfo.lastName,
        subtotalAmount: subtotalAmount,
        discountAmount: discountAmount,
        totalAmount: finalAmount,
        couponId: coupon?.id || null,
        couponCode: coupon?.code || null,
        status: 'PENDING',
        paymentStatus: 'PENDING',
        items: {
          create: orderItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price
          }))
        }
      },
      include: {
        items: {
          include: {
            product: true
          }
        }
      }
    })

    // Create Stripe payment intent
    const paymentIntent = await createPaymentIntent(
      finalAmount,
      'usd',
      {
        orderId: order.id,
        userId: session?.user?.id || null,
        customerEmail: customerInfo.email
      }
    )

    // Update order with payment intent ID and create coupon usage if applicable
    await prisma.$transaction(async (tx) => {
      await tx.order.update({
        where: { id: order.id },
        data: { paymentIntentId: paymentIntent.id }
      })

      // Create coupon usage record if coupon was applied
      if (coupon) {
        await tx.couponUsage.create({
          data: {
            couponId: coupon.id,
            userId: session?.user?.id || null,
            orderId: order.id
          }
        })

        // Increment coupon usage count
        await tx.coupon.update({
          where: { id: coupon.id },
          data: { usageCount: { increment: 1 } }
        })
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        orderId: order.id,
        subtotal: subtotalAmount,
        discount: discountAmount,
        total: finalAmount,
        coupon: coupon ? {
          code: coupon.code,
          name: coupon.name,
          type: coupon.type,
          value: coupon.value
        } : null
      }
    }, {
      headers: corsHeaders
    })

  } catch (error: any) {
    console.error('Error creating payment intent:', error)

    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, {
        status: 400,
        headers: corsHeaders
      })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create payment intent'
    }, {
      status: 500,
      headers: corsHeaders
    })
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
