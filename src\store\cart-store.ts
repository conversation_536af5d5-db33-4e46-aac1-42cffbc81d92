import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { ProductWithRelations } from '@/types'

// Custom serializer to handle BigInt values
const customStorage = {
  getItem: (name: string) => {
    const str = localStorage.getItem(name)
    if (!str) return null
    try {
      return JSON.parse(str, (key, value) => {
        // Convert string representations back to BigInt
        if (typeof value === 'string' && value.match(/^\d+n$/)) {
          return BigInt(value.slice(0, -1))
        }
        return value
      })
    } catch {
      return null
    }
  },
  setItem: (name: string, value: any) => {
    try {
      const str = JSON.stringify(value, (key, value) => {
        // Convert BigInt to string with 'n' suffix
        if (typeof value === 'bigint') {
          return value.toString() + 'n'
        }
        return value
      })
      localStorage.setItem(name, str)
    } catch (error) {
      console.error('Failed to serialize cart data:', error)
    }
  },
  removeItem: (name: string) => {
    localStorage.removeItem(name)
  }
}

// Helper function to sanitize product data for storage
const sanitizeProduct = (product: ProductWithRelations): ProductWithRelations => {
  return {
    ...product,
    // Convert BigInt to number for storage
    fileSize: product.fileSize ? Number(product.fileSize) : null,
    // Ensure dates are properly handled
    createdAt: new Date(product.createdAt),
    updatedAt: new Date(product.updatedAt),
  } as ProductWithRelations
}

export interface CartItem {
  id: string
  product: ProductWithRelations
  quantity: number
  addedAt: Date
}

interface CartStore {
  items: CartItem[]
  isOpen: boolean
  
  // Actions
  addItem: (product: ProductWithRelations, quantity?: number) => void
  removeItem: (productId: string) => void
  updateQuantity: (productId: string, quantity: number) => void
  clearCart: () => void
  toggleCart: () => void
  openCart: () => void
  closeCart: () => void
  
  // Computed values
  getTotalItems: () => number
  getTotalPrice: () => number
  getSubtotal: () => number
  getItemCount: (productId: string) => number
  hasItem: (productId: string) => boolean
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,

      addItem: (product, quantity = 1) => {
        const { items } = get()
        const sanitizedProduct = sanitizeProduct(product)
        const existingItem = items.find(item => item.product.id === product.id)

        if (existingItem) {
          // Update quantity if item already exists
          set({
            items: items.map(item =>
              item.product.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          })
        } else {
          // Add new item
          const newItem: CartItem = {
            id: `${product.id}-${Date.now()}`,
            product: sanitizedProduct,
            quantity,
            addedAt: new Date()
          }
          set({ items: [...items, newItem] })
        }
      },

      removeItem: (productId) => {
        set({
          items: get().items.filter(item => item.product.id !== productId)
        })
      },

      updateQuantity: (productId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(productId)
          return
        }

        set({
          items: get().items.map(item =>
            item.product.id === productId
              ? { ...item, quantity }
              : item
          )
        })
      },

      clearCart: () => {
        set({ items: [] })
      },

      toggleCart: () => {
        set({ isOpen: !get().isOpen })
      },

      openCart: () => {
        set({ isOpen: true })
      },

      closeCart: () => {
        set({ isOpen: false })
      },

      getTotalItems: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0)
      },

      getTotalPrice: () => {
        return get().items.reduce((total, item) => {
          const price = item.product.isOnSale && item.product.salePrice
            ? item.product.salePrice
            : item.product.price
          return total + (Number(price) * item.quantity)
        }, 0)
      },

      getSubtotal: () => {
        return get().getTotalPrice()
      },

      getItemCount: (productId) => {
        const item = get().items.find(item => item.product.id === productId)
        return item ? item.quantity : 0
      },

      hasItem: (productId) => {
        return get().items.some(item => item.product.id === productId)
      }
    }),
    {
      name: 'cart-storage',
      storage: customStorage,
      partialize: (state) => ({ items: state.items }),
    }
  )
)
